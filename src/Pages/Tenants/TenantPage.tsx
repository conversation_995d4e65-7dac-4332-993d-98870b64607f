import { Box, IconButton, InputAdornment, Stack, TextField, Typography } from '@mui/material';
import EyeIcon from 'Assets/EyeIcon';
import { Table, getBackendColumnName } from 'Components/Table';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Add, Search } from '@mui/icons-material';
import { CellContext } from '@tanstack/react-table';
import DotIcon from 'Assets/DotIcon';
import EditIcon from 'Assets/EditIcon';
import FilterIcon from 'Assets/FilterIcon';
import Button from 'Components/Button';
import { useGetTenantsCountQuery, useGetTenantsQuery } from 'redux/app/tenantManagementApiSlice';
import { actionStyles, bodyCellProps, buttonStyle, coloumnCellProps, headerBoxStyle, leftHeaderStyle, tableHeadProps } from 'styles/pages/TenantPage.styles';
import { DEFAULT_LIMIT, DEFAULT_OFFSET, tenantTableColumns } from './tenants.utils';



interface IActionButtonsProps {
  row: CellContext<unknown, unknown>;
}


export const ActionButtons: React.FC<IActionButtonsProps> = ({ row }) => {
  const handleEditBtn = (): void => {
    console.log('Edit', row);
  };

  return (
    <Stack display="flex" flexDirection={'row'}>
      <EyeIcon sx={actionStyles} />
      <EditIcon sx={actionStyles} />
      <DotIcon sx={actionStyles} />
    </Stack>
  );
};

const Tenant: React.FC = () => {
  const [limit, setLimit] = useState(DEFAULT_LIMIT);
  const [searchTerm, setSearchTerm] = useState('');
  const [offset, setOffset] = useState(DEFAULT_OFFSET);
  const [sortBy, setSortBy] = useState<string | null>(null);

  const handleSortChange = (columnId: string, desc: boolean) => {
    // Map frontend column name to backend column name
    const backendColumnName = getBackendColumnName(columnId);
    const sortParam = `${backendColumnName} ${desc ? 'DESC' : 'ASC'}`;
    setSortBy(sortParam);
  };

  const handlePageChange = (page: number) => {
    const newOffset = (page - 1) * limit;
    setOffset(newOffset);
  };

  const handleRowsPerPageChange = (newLimit: number) => {
    setLimit(newLimit);
    setOffset(0); // Reset to first page when changing page size
  };

  // Build the filter object for the API call
  const filterParams = {
    limit,
    offset,
    ...(sortBy && { order: sortBy }),
    ...(searchTerm && { search: searchTerm }),
  };

  // Build count filter (without limit/offset)
  const countFilterParams = {
    ...(sortBy && { order: sortBy }),
    ...(searchTerm && { search: searchTerm }),
  };

  const { data: tenants, error, isLoading } = useGetTenantsQuery(filterParams);
  const { data: tenantsCount, error: countError, isLoading: countLoading } = useGetTenantsCountQuery(countFilterParams);
  const navigate = useNavigate();

  const handleRedirect = () => {
    navigate('/add-tenant');
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={headerBoxStyle}>
        <Typography variant='h6' sx={leftHeaderStyle}>
          Tenants
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            size="small"
            placeholder="Search tenant name"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search sx={{ color: '#6c757d', fontSize: 20 }} />
                </InputAdornment>
              ),
            }}
            sx={{
              width: 250,
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'white',
                fontSize: '14px',
              }
            }}
          />
          <IconButton
            sx={{
              border: '1px solid #dee2e6',
              backgroundColor: 'white',
              borderRadius: '4px',
              width: 40,
              height: 40
            }}
          >
            <FilterIcon sx={{ color: 'white', fontSize: 20 }} />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<Add></Add>}
            sx={buttonStyle}
          >
            {/* <PlusIcon sx={{ color: 'white', backgroundColor: 'white', mr: 1 }} /> */}
            Add Tenant
          </Button>
        </Box>
      </Box>
      <Box>
        {tenants && (
          <Table
            data={tenants || []}
            columns={tenantTableColumns}
            tablePropsObject={{
              tableHeadProps: { sx: tableHeadProps },
              columnCellProps: { sx: coloumnCellProps },
              tableContainerProps: { sx: { border: '1px solid #DBDBDB' } },
              bodyCellProps: { sx: bodyCellProps },
            }}
            limit={limit}
            setLimit={setLimit}
            offset={offset}
            setOffset={setOffset}
            count={tenantsCount?.count || 0}
            manualPagination={true}
            enableSorting={true}
            onSortChange={handleSortChange}
            enablePagination={true}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
          />
        )}
      </Box>


    </Box>


  );
};

export default Tenant;
