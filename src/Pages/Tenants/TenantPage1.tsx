import { Edit, MoreVert, Search, Visibility } from '@mui/icons-material';
import {
    Box,
    Button,
    IconButton,
    InputBase,
    MenuItem,
    Pagination,
    Paper,
    Select,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@mui/material';
import { useState } from 'react';

const tenants = [
    {
        name: 'BioNova Research Labs',
        status: 'Active',
        date: '3 Jul, 2025',
        plan: 'Remote Control',
    },
    {
        name: 'Genexa Pharmaceuticals',
        status: 'Inactive',
        date: '1 Jun, 2025',
        plan: 'Historian',
    },
    {
        name: 'Medisyn Therapeutics',
        status: 'Active',
        date: '15 May, 2025',
        plan: 'Historian',
    },
    // Add more data as needed
];

const statusColorMap = {
    Active: '#d4f8e8',
    Inactive: '#ffe3e3',
    'Failed Provisioning': '#ffe9cc',
    'Pending Provisioning': '#fff4cc',
    'Pending Onboarding': '#ffe0cc',
};

const rowsPerPageOptions = [10, 20, 50];

export default function TenantTable() {
    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(20);

    const paginatedData = tenants.slice((page - 1) * rowsPerPage, page * rowsPerPage);
    const totalPages = Math.ceil(tenants.length / rowsPerPage);

    return (
        <Box p={3}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">Tenants</Typography>
                <Button variant="contained">+ Add Tenant</Button>
            </Box>

            <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mb={2}
                component={Paper}
                px={2}
            >
                <InputBase placeholder="Search tenant name" startAdornment={<Search />} fullWidth />
            </Box>

            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell><b>Tenant name</b></TableCell>
                            <TableCell><b>Status</b></TableCell>
                            <TableCell><b>Created date</b></TableCell>
                            <TableCell><b>Plan name</b></TableCell>
                            <TableCell><b>Actions</b></TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {paginatedData.map((tenant, index) => (
                            <TableRow key={index}>
                                <TableCell>{tenant.name}</TableCell>
                                <TableCell>
                                    <Box
                                        px={1.5}
                                        py={0.5}
                                        borderRadius={2}
                                        bgcolor={statusColorMap[tenant.status] || '#eee'}
                                        display="inline-block"
                                    >
                                        <Typography variant="body2">{tenant.status}</Typography>
                                    </Box>
                                </TableCell>
                                <TableCell>{tenant.date}</TableCell>
                                <TableCell>{tenant.plan}</TableCell>
                                <TableCell>
                                    <IconButton size="small"><Visibility /></IconButton>
                                    <IconButton size="small"><Edit /></IconButton>
                                    <IconButton size="small"><MoreVert /></IconButton>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                <Box display="flex" alignItems="center">
                    <Typography variant="body2" mr={1}>Show</Typography>
                    <Select
                        size="small"
                        value={rowsPerPage}
                        onChange={(e) => {
                            setRowsPerPage(e.target.value);
                            setPage(1);
                        }}
                    >
                        {rowsPerPageOptions.map((option) => (
                            <MenuItem key={option} value={option}>{option}</MenuItem>
                        ))}
                    </Select>
                    <Typography variant="body2" ml={1}>entries</Typography>
                </Box>

                <Pagination
                    count={10}
                    page={1}
                    onChange={(e, value) => setPage(value)}
                    shape="rounded"
                    color="primary"
                />
            </Box>
        </Box>
    );
}
