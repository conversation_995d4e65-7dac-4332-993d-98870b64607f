import { CellContext } from '@tanstack/react-table';
import StatusChip from 'Components/StatusChip/StatusChip';
import { colors } from 'Providers/theme/colors';
import { ActionButtons } from './TenantPage';


export const DEFAULT_LIMIT = 5;
export const DEFAULT_OFFSET = 0;

export enum TenantStatus {
    ACTIVE, // Tenant is active and fully functional
    PENDINGPROVISION, // Tenant is awaiting provisioning
    PROVISIONING, // Tenant is currently being provisioned
    PROVISIONFAILED, // Provisioning process failed
    DEPROVISIONING, // Tenant is being deprovisioned
    INACTIVE, // Tenant is inactive
    PENDINGONBOARDING, // Tenant is active but not onboarded due to missing information
}
export const getStatusColor = (status: TenantStatus): string => {
    const statusColorMap: Record<TenantStatus, string> = {
        [TenantStatus.ACTIVE]: `#E2FFF1`,
        [TenantStatus.DEPROVISIONING]: `black`,
        [TenantStatus.PENDINGPROVISION]: `#FFF9E0`,
        [TenantStatus.INACTIVE]: `#FFE0E0`,
        [TenantStatus.PROVISIONFAILED]: `#FFE7DC`,
        [TenantStatus.PROVISIONING]: `#F3ECE4`,
        [TenantStatus.PENDINGONBOARDING]: `#F0F0FB`,
    };
    return statusColorMap[status] || colors.white;
};


export const getFontColor = (status: TenantStatus): string => {
    const statusColorMap: Record<TenantStatus, string> = {
        [TenantStatus.ACTIVE]: `#0D653B`,
        [TenantStatus.DEPROVISIONING]: `black`,
        [TenantStatus.PENDINGPROVISION]: `#736116`,
        [TenantStatus.INACTIVE]: `#A91417`,
        [TenantStatus.PROVISIONFAILED]: `#79310F`,
        [TenantStatus.PROVISIONING]: `#483014`,
        [TenantStatus.PENDINGONBOARDING]: `#17377F`,
    };
    return statusColorMap[status] || colors.white;
};

export const getStatusLabel = (status: TenantStatus | number): string => {
    const statusLabelMap: Record<TenantStatus | number, string> = {
        [TenantStatus.ACTIVE]: 'Active',
        [TenantStatus.DEPROVISIONING]: 'De-Provisioning',
        [TenantStatus.PENDINGPROVISION]: 'Pending Provision',
        [TenantStatus.INACTIVE]: 'Inactive',
        [TenantStatus.PROVISIONFAILED]: 'Failed Provision',
        [TenantStatus.PROVISIONING]: 'Provisioning',
        [TenantStatus.PENDINGONBOARDING]: 'Pending Onboarding',
    };
    return statusLabelMap[status] || '';
};

interface TenantTableRow {
    name: string;
    status: TenantStatus;
    createdOn: string;
    planName: string;
    [key: string]: any;
}

interface TenantTableColumn {
    header: string;
    accessorKey?: keyof TenantTableRow;
    id?: string;
    cell?: (context: CellContext<TenantTableRow, any>) => React.ReactNode;
}

export const tenantTableColumns: TenantTableColumn[] = [
    { header: 'Tenant name', accessorKey: 'name', id: 'tenantName' },
    {
        header: 'Status',
        accessorKey: 'status',
        id: 'status',
        cell: (row: { getValue: () => TenantStatus }) => {
            const status = row.getValue() as TenantStatus;
            const backgroundColor = getStatusColor(status);
            const color = getFontColor(status);
            const label = getStatusLabel(status);
            return <StatusChip label={label} backgroundColor={backgroundColor} color={color} />;
        },
    },
    {
        header: 'Created date',
        accessorKey: 'createdOn',
        id: 'createdDate',
        cell: ({ row }: CellContext<TenantTableRow, any>) => {
            const date = new Date(row.original.createdOn);
            const day = date.toLocaleDateString('en-GB', { day: '2-digit' });
            const month = date.toLocaleDateString('en-GB', { month: 'long' });
            const year = date.toLocaleDateString('en-GB', { year: 'numeric' });
            return `${day} ${month}, ${year}`;
        },
    },
    { header: 'Plan name', accessorKey: 'planName', id: 'planName' },
    {
        header: 'Actions',
        cell: ({ row }: CellContext<TenantTableRow, any>) => <ActionButtons row={row.original} />,
    },
];
