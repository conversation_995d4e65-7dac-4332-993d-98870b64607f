import { render, screen, fireEvent } from '@testing-library/react';
import { Table } from './Table';
import { ColumnDef } from '@tanstack/react-table';

// Mock data for testing
interface TestData {
  id: number;
  name: string;
  email: string;
}

const mockData: TestData[] = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>' },
  { id: 3, name: '<PERSON>', email: '<EMAIL>' },
];

const mockColumns: ColumnDef<TestData>[] = [
  { header: 'ID', accessorKey: 'id' },
  { header: 'Name', accessorKey: 'name' },
  { header: 'Email', accessorKey: 'email' },
];

describe('Table Pagination', () => {
  const mockSetLimit = jest.fn();
  const mockSetOffset = jest.fn();
  const mockOnPageChange = jest.fn();
  const mockOnRowsPerPageChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render pagination controls when enablePagination is true', () => {
    render(
      <Table
        data={mockData}
        columns={mockColumns}
        enablePagination={true}
        manualPagination={true}
        limit={20}
        offset={0}
        count={100}
        setLimit={mockSetLimit}
        setOffset={mockSetOffset}
        onPageChange={mockOnPageChange}
        onRowsPerPageChange={mockOnRowsPerPageChange}
      />
    );

    // Check if pagination controls are rendered
    expect(screen.getByText('Show')).toBeInTheDocument();
    expect(screen.getByText('entries')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Go to page 1/i })).toBeInTheDocument();
  });

  it('should call onRowsPerPageChange when rows per page is changed', () => {
    render(
      <Table
        data={mockData}
        columns={mockColumns}
        enablePagination={true}
        manualPagination={true}
        limit={20}
        offset={0}
        count={100}
        setLimit={mockSetLimit}
        setOffset={mockSetOffset}
        onPageChange={mockOnPageChange}
        onRowsPerPageChange={mockOnRowsPerPageChange}
      />
    );

    // Find and click the rows per page dropdown
    const dropdown = screen.getByDisplayValue('20');
    fireEvent.mouseDown(dropdown);
    
    // Select 10 rows per page
    const option10 = screen.getByText('10');
    fireEvent.click(option10);

    expect(mockOnRowsPerPageChange).toHaveBeenCalledWith(10);
    expect(mockSetLimit).toHaveBeenCalledWith(10);
    expect(mockSetOffset).toHaveBeenCalledWith(0);
  });

  it('should call onPageChange when page is changed', () => {
    render(
      <Table
        data={mockData}
        columns={mockColumns}
        enablePagination={true}
        manualPagination={true}
        limit={20}
        offset={0}
        count={100}
        setLimit={mockSetLimit}
        setOffset={mockSetOffset}
        onPageChange={mockOnPageChange}
        onRowsPerPageChange={mockOnRowsPerPageChange}
      />
    );

    // Find and click page 2
    const page2Button = screen.getByRole('button', { name: /Go to page 2/i });
    fireEvent.click(page2Button);

    expect(mockOnPageChange).toHaveBeenCalledWith(2);
    expect(mockSetOffset).toHaveBeenCalledWith(20);
  });

  it('should calculate correct page count based on total count and limit', () => {
    render(
      <Table
        data={mockData}
        columns={mockColumns}
        enablePagination={true}
        manualPagination={true}
        limit={20}
        offset={0}
        count={85}
        setLimit={mockSetLimit}
        setOffset={mockSetOffset}
        onPageChange={mockOnPageChange}
        onRowsPerPageChange={mockOnRowsPerPageChange}
      />
    );

    // Should show 5 pages (85 items / 20 per page = 4.25, rounded up to 5)
    expect(screen.getByRole('button', { name: /Go to page 5/i })).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /Go to page 6/i })).not.toBeInTheDocument();
  });

  it('should display current page correctly based on offset and limit', () => {
    render(
      <Table
        data={mockData}
        columns={mockColumns}
        enablePagination={true}
        manualPagination={true}
        limit={20}
        offset={40} // Should be page 3 (40 / 20 + 1)
        count={100}
        setLimit={mockSetLimit}
        setOffset={mockSetOffset}
        onPageChange={mockOnPageChange}
        onRowsPerPageChange={mockOnRowsPerPageChange}
      />
    );

    // Page 3 should be selected
    const page3Button = screen.getByRole('button', { name: /Go to page 3/i });
    expect(page3Button).toHaveClass('Mui-selected');
  });
});
