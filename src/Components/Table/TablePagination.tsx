import {
  Box,
  FormControl,
  MenuItem,
  Pagination,
  Select,
  Typography
} from '@mui/material';
import { memo, useCallback } from 'react';

export interface TablePaginationProps {
  currentPage: number;
  currentRowsPerPage: number;
  totalCount: number;
  rowsPerPageOptions?: number[];
  onPageChange?: (page: number) => void;
  onRowsPerPageChange?: (rowsPerPage: number) => void;
  setLimit?: (newLimit: number) => void;
  setOffset?: (newOffset: number) => void;
  showEntriesText?: boolean;
  disabled?: boolean;
}

const DEFAULT_ROWS_PER_PAGE_OPTIONS = [10, 20, 50];

// Styles extracted as constants for better performance
const CONTAINER_STYLES = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  p: 2,
  backgroundColor: 'white',
} as const;

const ROWS_PER_PAGE_CONTAINER_STYLES = {
  display: 'flex',
  alignItems: 'center',
  gap: 1,
} as const;

const TEXT_STYLES = {
  fontSize: '14px',
  color: '#495057',
} as const;

const SELECT_STYLES = {
  fontSize: '14px',
  minWidth: 60,
  '& .MuiOutlinedInput-notchedOutline': {
    border: '1px solid #dee2e6'
  }
} as const;

const PAGINATION_STYLES = {
  '& .MuiPaginationItem-root': {
    fontSize: '14px',
    minWidth: '32px',
    height: '32px',
    margin: '0 2px',
    color: '#495057',
    borderRadius: '4px',
    border: '1px solid #dee2e6',
    backgroundColor: 'white',
    '&:hover': {
      backgroundColor: '#e9ecef',
    },
    '&.Mui-selected': {
      backgroundColor: '#3f51b5',
      color: 'white',
      border: '1px solid #3f51b5',
      '&:hover': {
        backgroundColor: '#303f9f',
      },
    },
  },
  '& .MuiPaginationItem-ellipsis': {
    border: 'none',
    backgroundColor: 'transparent',
  },
  '& .MuiPaginationItem-previousNext': {
    backgroundColor: 'white',
    border: '1px solid #dee2e6',
    '&:hover': {
      backgroundColor: '#e9ecef',
    },
  },
} as const;

const TablePagination: React.FC<TablePaginationProps> = ({
  currentPage,
  currentRowsPerPage,
  totalCount,
  rowsPerPageOptions = DEFAULT_ROWS_PER_PAGE_OPTIONS,
  onPageChange,
  onRowsPerPageChange,
  setLimit,
  setOffset,
  showEntriesText = true,
  disabled = false,
}) => {
  const totalPages = Math.ceil(totalCount / currentRowsPerPage);

  const handleRowsPerPageChange = useCallback((newLimit: number) => {
    if (setLimit) {
      setLimit(newLimit);
    }
    if (setOffset) {
      setOffset(0); // Reset to first page
    }
    if (onRowsPerPageChange) {
      onRowsPerPageChange(newLimit);
    }
  }, [setLimit, setOffset, onRowsPerPageChange]);

  const handlePageChange = useCallback((_: unknown, page: number) => {
    const newOffset = (page - 1) * currentRowsPerPage;
    if (setOffset) {
      setOffset(newOffset);
    }
    if (onPageChange) {
      onPageChange(page);
    }
  }, [currentRowsPerPage, setOffset, onPageChange]);

  return (
    <Box sx={CONTAINER_STYLES}>
      {/* Rows per page selector */}
      <Box sx={ROWS_PER_PAGE_CONTAINER_STYLES}>
        {showEntriesText && (
          <Typography sx={TEXT_STYLES}>
            Show
          </Typography>
        )}
        <FormControl size="small" disabled={disabled}>
          <Select
            value={currentRowsPerPage}
            onChange={(e) => handleRowsPerPageChange(Number(e.target.value))}
            sx={SELECT_STYLES}
            disabled={disabled}
          >
            {rowsPerPageOptions.map((option) => (
              <MenuItem key={option} value={option}>
                {option}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        {showEntriesText && (
          <Typography sx={TEXT_STYLES}>
            entries
          </Typography>
        )}
      </Box>

      {/* Pagination controls */}
      <Pagination
        count={totalPages}
        page={currentPage}
        onChange={handlePageChange}
        shape="rounded"
        siblingCount={2}
        boundaryCount={1}
        disabled={disabled}
        sx={PAGINATION_STYLES}
      />
    </Box>
  );
};

export default memo(TablePagination);
