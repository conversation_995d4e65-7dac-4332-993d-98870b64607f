import { ApiSliceIdentifier } from 'Constants/enums';
import { apiSlice } from 'redux/apiSlice';

const apiSliceIdentifier = ApiSliceIdentifier.TENANT_FACADE;
export const tenantApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getTenants: builder.query({
      query: filterQuery => ({
        url: '/tenants',
        method: 'GET',
        params: filterQuery,
        apiSliceIdentifier,
      }),
    }),
    getTenantsCount: builder.query({
      query: () => ({
        url: '/tenants/count',
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
    verifyTenantKey: builder.mutation({
      query: KeyDto => ({
        url: '/tenants/verify-key',
        method: 'POST',
        body: KeyDto,
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const { useVerifyTenantKeyMutation, useGetTenantsQuery, useGetTenantsCountQuery, useLazyGetTenantsCountQuery, useLazyGetTenantsQuery } = tenantApiSlice;

